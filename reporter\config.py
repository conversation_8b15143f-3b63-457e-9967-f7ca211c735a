# config.py
from pydantic_settings import BaseSettings, SettingsConfigDict
class AppSettings(BaseSettings):
    MICROLENS_DATABASE_URL: str = ""
    FUXI_DATABASE_URL:str = ""
    HIVE_HOST: str = ""
    HIVE_PORT: int = 0
    WECOM_ROBOT_WEBHOOK_URL: str = ""

    # Email settings
    EMAIL_ADDRESS: str = "<EMAIL>"
    EMAIL_PASSWORD: str = "agent@2025"
    EMAIL_IMAP_SERVER: str = 'imap.qiye.aliyun.com'
    EMAIL_IMAP_PORT: int = 993

    model_config = SettingsConfigDict(
        env_file="./reporter/.env",
        env_file_encoding='utf-8',
        extra="allow"
    )

settings = AppSettings() # 实例化配置
